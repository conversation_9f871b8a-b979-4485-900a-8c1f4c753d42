#!/usr/bin/env python3
"""
支持端口协商的RDMA客户端
"""

from ctypes import *
import socket
import json
import re
import time

# 加载库
lib = CDLL("/lib/x86_64-linux-gnu/librdmacm.so")

# 定义函数签名
lib.rdma_create_event_channel.argtypes = []
lib.rdma_create_event_channel.restype = c_void_p

lib.rdma_create_id.argtypes = [c_void_p, POINTER(c_void_p), c_void_p, c_int]
lib.rdma_create_id.restype = c_int

lib.rdma_resolve_addr.argtypes = [c_void_p, c_void_p, c_void_p, c_int]
lib.rdma_resolve_addr.restype = c_int

lib.rdma_get_cm_event.argtypes = [c_void_p, POINTER(c_void_p)]
lib.rdma_get_cm_event.restype = c_int

lib.rdma_ack_cm_event.argtypes = [c_void_p]
lib.rdma_ack_cm_event.restype = c_int

lib.rdma_destroy_id.argtypes = [c_void_p]
lib.rdma_destroy_id.restype = c_int

lib.rdma_destroy_event_channel.argtypes = [c_void_p]
lib.rdma_destroy_event_channel.restype = None

# 定义sockaddr_in结构
class sockaddr_in(Structure):
    _fields_ = [
        ("sin_family", c_uint16),
        ("sin_port", c_uint16),
        ("sin_addr", c_uint32),
        ("sin_zero", c_uint8 * 8),
    ]

# 定义RDMA事件结构
class rdma_cm_event(Structure):
    _fields_ = [
        ("id", c_void_p),
        ("listen_id", c_void_p),
        ("event", c_int),
        ("status", c_int),
        ("private_data", c_void_p),
        ("private_data_len", c_uint8),
    ]

RDMA_PS_TCP = 0x0106

def discover_rdma_service(server_ip):
    """发现RDMA服务"""
    print(f"🔍 发现RDMA服务: {server_ip}")
    
    # 尝试常见的RDMA端口
    test_ports = [
        (7174, "rping默认端口"),
        (4791, "自定义RDMA端口"), 
        (8888, "UDP管理端口"),
        (18515, "IB默认端口")
    ]
    
    discovered_services = []
    
    for port, desc in test_ports:
        print(f"   测试 {port} ({desc})...")
        
        # 测试TCP连接
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((server_ip, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ TCP端口 {port} 可连接")
                discovered_services.append((port, "tcp", desc))
                continue
        except:
            pass
        
        # 测试UDP响应
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1)
            
            # 发送不同类型的探测消息
            test_messages = [
                b"GET_MR",
                b"RDMA_SERVICE_DISCOVERY", 
                b"rping",
                b"HELLO"
            ]
            
            for msg in test_messages:
                try:
                    sock.sendto(msg, (server_ip, port))
                    response, addr = sock.recvfrom(1024)
                    print(f"   ✅ UDP端口 {port} 响应: {response[:50]}...")
                    discovered_services.append((port, "udp", desc, response))
                    break
                except socket.timeout:
                    continue
            
            sock.close()
        except:
            pass
    
    return discovered_services

def negotiate_rdma_port(server_ip, services):
    """协商RDMA连接端口"""
    print(f"\n🤝 协商RDMA连接端口...")
    
    for service in services:
        port, protocol, desc = service[:3]
        print(f"   尝试服务: {port} ({protocol}) - {desc}")
        
        if protocol == "udp" and len(service) > 3:
            response = service[3]
            
            # 尝试解析响应中的端口信息
            try:
                response_str = response.decode('utf-8', errors='ignore')
                print(f"     响应内容: {response_str}")
                
                # 查找JSON格式
                try:
                    data = json.loads(response_str)
                    if 'rdma_port' in data:
                        rdma_port = int(data['rdma_port'])
                        print(f"   ✅ JSON中的RDMA端口: {rdma_port}")
                        return rdma_port
                    elif 'port' in data:
                        rdma_port = int(data['port'])
                        print(f"   ✅ JSON中的端口: {rdma_port}")
                        return rdma_port
                except:
                    pass
                
                # 查找端口号模式
                ports = re.findall(r'\b(?:479[0-9]|717[0-9]|[0-9]{4,5})\b', response_str)
                if ports:
                    rdma_port = int(ports[0])
                    print(f"   ✅ 提取的端口: {rdma_port}")
                    return rdma_port
                    
            except:
                pass
        
        elif protocol == "tcp":
            # TCP端口可能直接就是RDMA CM端口
            print(f"   ✅ 使用TCP端口作为RDMA端口: {port}")
            return port
    
    # 如果没有协商成功，使用默认端口
    print(f"   ⚠️  使用默认端口: 7174")
    return 7174

def test_rdma_connection(server_ip, rdma_port):
    """测试RDMA连接"""
    print(f"\n🔗 测试RDMA连接到 {server_ip}:{rdma_port}")
    
    try:
        # 1. 创建事件通道
        channel = lib.rdma_create_event_channel()
        if not channel:
            print("❌ 创建事件通道失败")
            return False
        
        # 2. 创建CM ID
        cm_id = c_void_p()
        ret = lib.rdma_create_id(channel, byref(cm_id), None, RDMA_PS_TCP)
        if ret != 0:
            print(f"❌ 创建CM ID失败: {ret}")
            lib.rdma_destroy_event_channel(channel)
            return False
        
        # 3. 构造目标地址
        dst_addr = sockaddr_in()
        dst_addr.sin_family = socket.AF_INET
        dst_addr.sin_port = socket.htons(rdma_port)
        dst_addr.sin_addr = struct.unpack("!I", socket.inet_aton(server_ip))[0]
        
        # 4. 解析地址
        print("   解析地址...")
        ret = lib.rdma_resolve_addr(cm_id.value, None, byref(dst_addr), 2000)
        if ret != 0:
            print(f"❌ 地址解析失败: {ret}")
            lib.rdma_destroy_id(cm_id.value)
            lib.rdma_destroy_event_channel(channel)
            return False
        
        # 5. 等待地址解析事件
        print("   等待地址解析事件...")
        event_ptr = c_void_p()
        ret = lib.rdma_get_cm_event(channel, byref(event_ptr))
        if ret != 0:
            print(f"❌ 获取事件失败: {ret}")
            lib.rdma_destroy_id(cm_id.value)
            lib.rdma_destroy_event_channel(channel)
            return False
        
        # 6. 检查事件类型
        event = cast(event_ptr, POINTER(rdma_cm_event)).contents
        print(f"   收到事件: {event.event}")
        
        if event.event == 1:  # RDMA_CM_EVENT_ADDR_RESOLVED
            print("✅ 地址解析成功！")
            success = True
        else:
            print(f"❌ 意外的事件: {event.event}")
            success = False
        
        # 7. 清理
        lib.rdma_ack_cm_event(event_ptr)
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        
        return success
        
    except Exception as e:
        print(f"❌ RDMA连接测试异常: {e}")
        return False

def main():
    # 尝试两个网络地址
    server_addresses = [
        ("***********", "管理网络"),
        ("**********", "RDMA网络")
    ]
    
    print("🚀 支持端口协商的RDMA客户端")
    print("=" * 50)
    
    # 1. 服务发现
    services = discover_rdma_service(server_ip)
    if not services:
        print("❌ 未发现任何RDMA服务")
        return
    
    print(f"\n📋 发现的服务:")
    for i, service in enumerate(services):
        port, protocol, desc = service[:3]
        print(f"   {i+1}. 端口 {port} ({protocol}) - {desc}")
    
    # 2. 端口协商
    rdma_port = negotiate_rdma_port(server_ip, services)
    
    # 3. RDMA连接测试
    success = test_rdma_connection(server_ip, rdma_port)
    
    print(f"\n🎯 测试结果:")
    print(f"   服务发现: ✅ (找到 {len(services)} 个服务)")
    print(f"   端口协商: ✅ (端口 {rdma_port})")
    print(f"   RDMA连接: {'✅' if success else '❌'}")
    
    if success:
        print(f"\n🎉 RDMA连接验证成功！")
        print(f"   服务器: {server_ip}")
        print(f"   端口: {rdma_port}")
    else:
        print(f"\n❌ RDMA连接验证失败")

if __name__ == "__main__":
    main()
