#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <rdma/rdma_cma.h>
#include <rdma/rdma_verbs.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cjson/cJSON.h>

#define SERVER_PORT 4791
#define UDP_PORT 8888
#define BUFFER_SIZE 1024

struct rdma_client_context {
    struct rdma_cm_id *cm_id;
    struct ibv_mr *mr;
    char *buffer;
    uint64_t remote_addr;
    uint32_t remote_rkey;
};

// 获取服务器MR信息
int get_server_mr_info(const char *server_ip, uint64_t *remote_addr, uint32_t *remote_rkey) {
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        perror("socket");
        return -1;
    }

    struct sockaddr_in server_addr = {0};
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(UDP_PORT);
    inet_pton(AF_INET, server_ip, &server_addr.sin_addr);

    // 发送GET_MR请求
    const char *request = "GET_MR";
    if (sendto(sockfd, request, strlen(request), 0, 
               (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("sendto");
        close(sockfd);
        return -1;
    }

    // 接收响应
    char response[512];
    ssize_t recv_len = recvfrom(sockfd, response, sizeof(response)-1, 0, NULL, NULL);
    if (recv_len < 0) {
        perror("recvfrom");
        close(sockfd);
        return -1;
    }
    response[recv_len] = '\0';
    close(sockfd);

    // 解析JSON响应
    cJSON *json = cJSON_Parse(response);
    if (!json) {
        fprintf(stderr, "Failed to parse JSON response\n");
        return -1;
    }

    cJSON *addr_item = cJSON_GetObjectItem(json, "mr_addr");
    cJSON *rkey_item = cJSON_GetObjectItem(json, "rkey");

    if (!addr_item || !rkey_item) {
        fprintf(stderr, "Missing mr_addr or rkey in response\n");
        cJSON_Delete(json);
        return -1;
    }

    *remote_addr = strtoull(cJSON_GetStringValue(addr_item), NULL, 16);
    *remote_rkey = cJSON_GetNumberValue(rkey_item);

    cJSON_Delete(json);
    printf("✅ 获取服务器MR信息: addr=0x%lx, rkey=%u\n", *remote_addr, *remote_rkey);
    return 0;
}

// 建立RDMA连接
int establish_rdma_connection(struct rdma_client_context *ctx, const char *server_ip) {
    struct rdma_event_channel *ec = rdma_create_event_channel();
    if (!ec) {
        perror("rdma_create_event_channel");
        return -1;
    }

    if (rdma_create_id(ec, &ctx->cm_id, NULL, RDMA_PS_TCP)) {
        perror("rdma_create_id");
        return -1;
    }

    struct sockaddr_in addr = {0};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(SERVER_PORT);
    inet_pton(AF_INET, server_ip, &addr.sin_addr);

    if (rdma_resolve_addr(ctx->cm_id, NULL, (struct sockaddr*)&addr, 2000)) {
        perror("rdma_resolve_addr");
        return -1;
    }

    // 等待地址解析完成
    struct rdma_cm_event *event;
    if (rdma_get_cm_event(ec, &event)) {
        perror("rdma_get_cm_event");
        return -1;
    }

    if (event->event != RDMA_CM_EVENT_ADDR_RESOLVED) {
        fprintf(stderr, "Address resolution failed\n");
        rdma_ack_cm_event(event);
        return -1;
    }
    rdma_ack_cm_event(event);

    if (rdma_resolve_route(ctx->cm_id, 2000)) {
        perror("rdma_resolve_route");
        return -1;
    }

    // 等待路由解析完成
    if (rdma_get_cm_event(ec, &event)) {
        perror("rdma_get_cm_event");
        return -1;
    }

    if (event->event != RDMA_CM_EVENT_ROUTE_RESOLVED) {
        fprintf(stderr, "Route resolution failed\n");
        rdma_ack_cm_event(event);
        return -1;
    }
    rdma_ack_cm_event(event);

    printf("✅ RDMA地址和路由解析成功\n");
    return 0;
}

// 创建QP和MR
int setup_rdma_resources(struct rdma_client_context *ctx) {
    // 分配缓冲区
    ctx->buffer = malloc(BUFFER_SIZE);
    if (!ctx->buffer) {
        perror("malloc");
        return -1;
    }

    // 创建保护域
    struct ibv_pd *pd = ibv_alloc_pd(ctx->cm_id->verbs);
    if (!pd) {
        perror("ibv_alloc_pd");
        return -1;
    }

    // 注册内存区域
    ctx->mr = ibv_reg_mr(pd, ctx->buffer, BUFFER_SIZE,
                        IBV_ACCESS_LOCAL_WRITE | 
                        IBV_ACCESS_REMOTE_READ | 
                        IBV_ACCESS_REMOTE_WRITE);
    if (!ctx->mr) {
        perror("ibv_reg_mr");
        return -1;
    }

    // 创建完成队列
    struct ibv_cq *cq = ibv_create_cq(ctx->cm_id->verbs, 16, NULL, NULL, 0);
    if (!cq) {
        perror("ibv_create_cq");
        return -1;
    }

    // 创建QP
    struct ibv_qp_init_attr qp_attr = {0};
    qp_attr.send_cq = cq;
    qp_attr.recv_cq = cq;
    qp_attr.qp_type = IBV_QPT_RC;
    qp_attr.cap.max_send_wr = 16;
    qp_attr.cap.max_recv_wr = 16;
    qp_attr.cap.max_send_sge = 1;
    qp_attr.cap.max_recv_sge = 1;

    if (rdma_create_qp(ctx->cm_id, pd, &qp_attr)) {
        perror("rdma_create_qp");
        return -1;
    }

    printf("✅ RDMA资源创建成功\n");
    return 0;
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        fprintf(stderr, "Usage: %s <server_ip>\n", argv[0]);
        return 1;
    }

    const char *server_ip = argv[1];
    struct rdma_client_context ctx = {0};

    printf("🚀 开始RDMA客户端测试...\n");

    // 1. 获取服务器MR信息
    if (get_server_mr_info(server_ip, &ctx.remote_addr, &ctx.remote_rkey) < 0) {
        fprintf(stderr, "❌ 获取服务器MR信息失败\n");
        return 1;
    }

    // 2. 建立RDMA连接
    if (establish_rdma_connection(&ctx, server_ip) < 0) {
        fprintf(stderr, "❌ 建立RDMA连接失败\n");
        return 1;
    }

    // 3. 设置RDMA资源
    if (setup_rdma_resources(&ctx) < 0) {
        fprintf(stderr, "❌ 设置RDMA资源失败\n");
        return 1;
    }

    printf("🎉 RDMA客户端初始化完成！\n");
    printf("📋 本地MR: addr=%p, lkey=%u\n", ctx.buffer, ctx.mr->lkey);
    printf("📋 远程MR: addr=0x%lx, rkey=%u\n", ctx.remote_addr, ctx.remote_rkey);

    // 清理资源
    if (ctx.mr) ibv_dereg_mr(ctx.mr);
    if (ctx.buffer) free(ctx.buffer);
    if (ctx.cm_id) rdma_destroy_id(ctx.cm_id);

    return 0;
}
