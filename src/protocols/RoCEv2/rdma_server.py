from pyverbs import device, pd, cq, qp, mr, enums
from pyverbs.addr import AHAttr
import socket

class RDMAServer:
    def __init__(self, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备（自动降级到 Soft-RoCEv2）
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP 并初始化状态
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存区域（MR）
        # MR构造函数: MR(creator, length, access, address=None)
        self.buf_size = buf_size
        access_flags = (enums.IBV_ACCESS_LOCAL_WRITE |
                       enums.IBV_ACCESS_REMOTE_READ |
                       enums.IBV_ACCESS_REMOTE_WRITE)
        self.mr = mr.MR(self.pd, self.buf_size, access_flags)
        print(f"[Server] MR created: addr={self.mr.buf}, lkey={self.mr.lkey}, rkey={self.mr.rkey}")
        
        # 4. 用 TCP 套接字交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.bind(('0.0.0.0', port))
        self.sock.listen(1)
        print(f"[Server] Waiting for client connection on port {port}...")

    def run(self):
        conn, addr = self.sock.accept()
        print(f"[Server] Client connected: {addr}")
        
        # 1. 获取本地连接参数
        # 尝试获取有效的GID
        gid_obj = None
        gid_str = None
        for gid_index in [1, 0]:  # 先尝试索引1，再尝试索引0
            try:
                gid_obj = self.dev.query_gid(1, gid_index)  # port_num, gid_index
                gid_str = str(gid_obj)  # 转换为字符串
                print(f"[Server] Local GID[{gid_index}]: {gid_str}")
                # 检查是否是有效的非零GID
                if gid_str and gid_str != "0000:0000:0000:0000:0000:0000:0000:0000":
                    print(f"[Server] Using GID index {gid_index}")
                    break
            except Exception as e:
                print(f"[Server] Failed to get GID[{gid_index}]: {e}")
                continue

        if not gid_str or gid_str == "0000:0000:0000:0000:0000:0000:0000:0000":
            # 如果获取失败或全零，使用默认GID
            gid_str = "0000:0000:0000:0000:0000:0000:0000:0000"
            print(f"[Server] Using default GID (all zeros)")

        # 将GID字符串转换为bytes用于传输
        gid_bytes = gid_str.encode('utf-8')

        try:
            # 获取端口属性，包含LID
            port_attr = self.dev.query_port(1)
            lid = port_attr.lid
            print(f"[Server] Local LID: {lid}")
        except:
            # 如果获取失败，使用默认LID
            lid = 1
            print(f"[Server] Using default LID: {lid}")

        # 发送 Server 的完整 QP 参数（LID, QPN, PSN, GID）
        server_qp_info = {
            'lid': lid,
            'qpn': self.qp.qp_num,
            'psn': 0x1234,  # 初始 PSN
            'gid': gid_bytes  # 使用bytes格式传输
        }
        conn.sendall(str(server_qp_info).encode())
        
        # 2. 接收 Client 的 QP 参数
        client_qp_info = eval(conn.recv(1024).decode())
        print(f"[Server] Received client QP info: {client_qp_info}")
        
        # 3. 配置 QP 到 RTR（Ready to Receive）
        qp_attr = qp.QPAttr()
        qp_attr.qp_state = enums.IBV_QPS_RTR
        qp_attr.path_mtu = enums.IBV_MTU_1024
        qp_attr.dest_qp_num = client_qp_info['qpn']
        qp_attr.rq_psn = client_qp_info['psn']
        qp_attr.max_dest_rd_atomic = 1
        qp_attr.min_rnr_timer = 12
        # 配置AH属性（使用全局路由）
        ah_attr = AHAttr(port_num=1)

        # 处理客户端GID
        client_gid_data = client_qp_info['gid']
        if isinstance(client_gid_data, bytes):
            try:
                client_gid_str = client_gid_data.decode('utf-8')
                print(f"[Server] Client GID: {client_gid_str}")
            except:
                client_gid_str = "0000:0000:0000:0000:0000:0000:0000:0000"
                print(f"[Server] Using default client GID: {client_gid_str}")
        else:
            client_gid_str = str(client_gid_data)
            print(f"[Server] Client GID: {client_gid_str}")

        # 尝试本地路由（软件RoCEv2可能更适合）
        ah_attr.is_global = 0  # 禁用全局路由
        ah_attr.dlid = client_qp_info.get('lid', 1)  # 使用客户端LID
        ah_attr.sl = 0
        qp_attr.ah_attr = ah_attr
        print(f"[Server] Using global routing with client GID")

        self.qp.to_rtr(qp_attr)
        
        # 4. 配置 QP 到 RTS（Ready to Send）
        qp_attr_rts = qp.QPAttr()
        qp_attr_rts.qp_state = enums.IBV_QPS_RTS
        qp_attr_rts.timeout = 12
        qp_attr_rts.retry_cnt = 7
        qp_attr_rts.rnr_retry = 7
        qp_attr_rts.sq_psn = server_qp_info['psn']
        qp_attr_rts.max_rd_atomic = 1

        self.qp.to_rts(qp_attr_rts)
        
        print("[Server] RDMA connection established!")
        
        # 5. 处理 RDMA 操作（示例：等待客户端写数据）
        print("[Server] Waiting for RDMA operations...")
        while True:
            try:
                wc_list = self.cq.poll(1)  # poll返回列表
                if wc_list:
                    for wc in wc_list:
                        if wc.status == enums.IBV_WC_SUCCESS:
                            if wc.opcode == enums.IBV_WC_RDMA_WRITE:
                                # 从MR读取数据
                                data = self.mr.read(10, 0)  # 读取前10字节
                                print(f"[Server] Received RDMA Write: {data}")
                            else:
                                print(f"[Server] Received operation: {wc.opcode}")
                        else:
                            print(f"[Server] Work completion error: {wc.status}")
            except Exception as e:
                print(f"[Server] Error polling CQ: {e}")
                break

if __name__ == "__main__":
    server = RDMAServer()
    server.run()