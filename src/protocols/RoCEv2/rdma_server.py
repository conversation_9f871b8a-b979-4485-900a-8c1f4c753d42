from pyverbs import device, pd, cq, qp, mr, enums
from pyverbs.addr import AHAttr
import socket

class RDMAServer:
    def __init__(self, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备（自动降级到 Soft-RoCEv2）
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP 并初始化状态
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存区域（MR）
        # MR构造函数: MR(creator, length, access, address=None)
        self.buf_size = buf_size
        access_flags = (enums.IBV_ACCESS_LOCAL_WRITE |
                       enums.IBV_ACCESS_REMOTE_READ |
                       enums.IBV_ACCESS_REMOTE_WRITE)
        self.mr = mr.MR(self.pd, self.buf_size, access_flags)
        print(f"[Server] MR created: addr={self.mr.buf}, lkey={self.mr.lkey}, rkey={self.mr.rkey}")
        
        # 4. 用 TCP 套接字交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.bind(('0.0.0.0', port))
        self.sock.listen(1)
        print(f"[Server] Waiting for client connection on port {port}...")

    def run(self):
        conn, addr = self.sock.accept()
        print(f"[Server] Client connected: {addr}")
        
        # 1. 获取本地GID
        try:
            # 获取端口1的GID表
            gid = self.ctx.query_gid(port=1, gid_index=0)
            print(f"[Server] Local GID: {gid}")
        except:
            # 如果获取失败，使用默认GID
            gid = bytes(16)  # 全零GID
            print(f"[Server] Using default GID (all zeros)")

        # 发送 Server 的 QP 参数（QPN, PSN, GID）
        server_qp_info = {
            'qpn': self.qp.qp_num,
            'psn': 0x1234,  # 初始 PSN
            'gid': gid
        }
        conn.sendall(str(server_qp_info).encode())
        
        # 2. 接收 Client 的 QP 参数
        client_qp_info = eval(conn.recv(1024).decode())
        print(f"[Server] Received client QP info: {client_qp_info}")
        
        # 3. 配置 QP 到 RTR（Ready to Receive）
        qp_attr = qp.QPAttr()
        qp_attr.qp_state = enums.IBV_QPS_RTR
        qp_attr.path_mtu = enums.IBV_MTU_1024
        qp_attr.dest_qp_num = client_qp_info['qpn']
        qp_attr.rq_psn = client_qp_info['psn']
        qp_attr.max_dest_rd_atomic = 1
        qp_attr.min_rnr_timer = 12
        # 配置AH属性（简化版本，适用于软件RoCEv2）
        ah_attr = AHAttr(port_num=1)
        ah_attr.is_global = 0  # 禁用全局路由，使用本地路由
        ah_attr.dlid = 1  # 目标LID（本地）
        ah_attr.sl = 0  # 服务级别
        qp_attr.ah_attr = ah_attr

        self.qp.to_rtr(qp_attr)
        
        # 4. 配置 QP 到 RTS（Ready to Send）
        qp_attr_rts = qp.QPAttr()
        qp_attr_rts.qp_state = enums.IBV_QPS_RTS
        qp_attr_rts.timeout = 12
        qp_attr_rts.retry_cnt = 7
        qp_attr_rts.rnr_retry = 7
        qp_attr_rts.sq_psn = server_qp_info['psn']
        qp_attr_rts.max_rd_atomic = 1

        self.qp.to_rts(qp_attr_rts)
        
        print("[Server] RDMA connection established!")
        
        # 5. 处理 RDMA 操作（示例：等待客户端写数据）
        print("[Server] Waiting for RDMA operations...")
        while True:
            try:
                wc_list = self.cq.poll(1)  # poll返回列表
                if wc_list:
                    for wc in wc_list:
                        if wc.status == enums.IBV_WC_SUCCESS:
                            if wc.opcode == enums.IBV_WC_RDMA_WRITE:
                                # 从MR读取数据
                                data = self.mr.read(10, 0)  # 读取前10字节
                                print(f"[Server] Received RDMA Write: {data}")
                            else:
                                print(f"[Server] Received operation: {wc.opcode}")
                        else:
                            print(f"[Server] Work completion error: {wc.status}")
            except Exception as e:
                print(f"[Server] Error polling CQ: {e}")
                break

if __name__ == "__main__":
    server = RDMAServer()
    server.run()