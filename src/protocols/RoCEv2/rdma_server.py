from pyverbs import device, pd, cq, qp, mr, enums
import socket

class RDMAServer:
    def __init__(self, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备（自动降级到 Soft-RoCEv2）
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP 并初始化状态
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存区域（MR）
        self.buf = bytearray(buf_size)
        self.mr = mr.MR(
            self.pd,
            self.buf,
            len(self.buf),
            enums.IBV_ACCESS_LOCAL_WRITE | enums.IBV_ACCESS_REMOTE_READ | enums.IBV_ACCESS_REMOTE_WRITE
        )
        
        # 4. 用 TCP 套接字交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.bind(('0.0.0.0', port))
        self.sock.listen(1)
        print(f"[Server] Waiting for client connection on port {port}...")

    def run(self):
        conn, addr = self.sock.accept()
        print(f"[Server] Client connected: {addr}")
        
        # 1. 发送 Server 的 QP 参数（QPN, PSN, GID）
        server_qp_info = {
            'qpn': self.qp.qp_num,
            'psn': 0x1234,  # 初始 PSN
            'gid': bytes(16)  # 默认 GID（Soft-RoCEv2 可留空）
        }
        conn.sendall(str(server_qp_info).encode())
        
        # 2. 接收 Client 的 QP 参数
        client_qp_info = eval(conn.recv(1024).decode())
        print(f"[Server] Received client QP info: {client_qp_info}")
        
        # 3. 配置 QP 到 RTR（Ready to Receive）
        self.qp.modify(
            qp.QPState.RTR,
            enums.IBV_QP_STATE | enums.IBV_QP_AV | enums.IBV_QP_PATH_MTU |
            enums.IBV_QP_DEST_QPN | enums.IBV_QP_RQ_PSN | enums.IBV_QP_MAX_DEST_RD_ATOMIC,
            qp.QPAttr(
                ah_attr=qp.AHAttr(
                    is_global=0,
                    port_num=1
                ),
                dest_qpn=client_qp_info['qpn'],
                path_mtu=enums.IBV_MTU_1024,
                rq_psn=client_qp_info['psn']
            )
        )
        
        # 4. 配置 QP 到 RTS（Ready to Send）
        self.qp.modify(
            qp.QPState.RTS,
            enums.IBV_QP_STATE | enums.IBV_QP_SQ_PSN | enums.IBV_QP_TIMEOUT,
            qp.QPAttr(
                sq_psn=server_qp_info['psn'],
                timeout=12
            )
        )
        
        print("[Server] RDMA connection established!")
        
        # 5. 处理 RDMA 操作（示例：等待客户端写数据）
        while True:
            wc = self.cq.poll(1)
            if wc and wc.status == enums.IBV_WC_SUCCESS:
                if wc.opcode == enums.IBV_WC_RDMA_WRITE:
                    print(f"[Server] Received RDMA Write: {self.buf[:10]}")

if __name__ == "__main__":
    server = RDMAServer()
    server.run()