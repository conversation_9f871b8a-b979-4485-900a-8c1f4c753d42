#!/usr/bin/env python3
"""
简单的RDMA连接测试工具
"""

import socket
import json
import time
from pyverbs.device import get_device_list, Context
from pyverbs.pd import PD
from pyverbs.mr import MR
from pyverbs.enums import IBV_ACCESS_LOCAL_WRITE, IBV_ACCESS_REMOTE_READ, IBV_ACCESS_REMOTE_WRITE

def test_udp_connection(server_ip, port=8888):
    """测试UDP连接"""
    try:
        print(f"🔍 测试UDP连接到 {server_ip}:{port}")
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        sock.sendto(b"GET_MR", (server_ip, port))
        response = sock.recv(1024).decode('utf-8')
        sock.close()
        
        mr_info = json.loads(response)
        print(f"✅ UDP连接成功")
        print(f"   服务器MR: {mr_info}")
        return True, mr_info
    except Exception as e:
        print(f"❌ UDP连接失败: {e}")
        return False, None

def test_tcp_connection(server_ip, port=4791):
    """测试TCP连接到RDMA端口"""
    try:
        print(f"🔍 测试TCP连接到 {server_ip}:{port}")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((server_ip, port))
        sock.close()
        
        if result == 0:
            print(f"✅ TCP连接成功")
            return True
        else:
            print(f"❌ TCP连接失败，错误码: {result}")
            return False
    except Exception as e:
        print(f"❌ TCP连接异常: {e}")
        return False

def test_rdma_devices():
    """测试本地RDMA设备"""
    try:
        print("🔍 检查本地RDMA设备")
        dev_list = get_device_list()
        if not dev_list:
            print("❌ 未找到RDMA设备")
            return False
        
        for i, dev in enumerate(dev_list):
            print(f"   设备 {i}: {dev.name.decode('utf-8')}")
        
        # 测试第一个设备
        ctx = Context(name=dev_list[0].name.decode('utf-8'))
        pd = PD(ctx)
        mr = MR(pd, 1024, IBV_ACCESS_LOCAL_WRITE | IBV_ACCESS_REMOTE_READ | IBV_ACCESS_REMOTE_WRITE)
        
        print(f"✅ RDMA设备正常")
        print(f"   本地MR: addr={mr.buf}, lkey={mr.lkey}")
        return True
    except Exception as e:
        print(f"❌ RDMA设备测试失败: {e}")
        return False

def main():
    print("🚀 RDMA连接诊断工具")
    print("=" * 50)
    
    # 测试本地RDMA设备
    rdma_ok = test_rdma_devices()
    print()
    
    # 测试UDP连接 (管理网络)
    udp_ok, mr_info = test_udp_connection("10.61.72.27", 8888)
    print()
    
    # 测试TCP连接到RDMA网络
    tcp_rdma_ok = test_tcp_connection("20.20.1.18", 4791)
    print()
    
    # 测试TCP连接到管理网络
    tcp_mgmt_ok = test_tcp_connection("10.61.72.27", 4791)
    print()
    
    print("📋 诊断结果:")
    print(f"   本地RDMA设备: {'✅' if rdma_ok else '❌'}")
    print(f"   UDP通信(管理网络): {'✅' if udp_ok else '❌'}")
    print(f"   TCP连接(RDMA网络): {'✅' if tcp_rdma_ok else '❌'}")
    print(f"   TCP连接(管理网络): {'✅' if tcp_mgmt_ok else '❌'}")
    
    if udp_ok and mr_info:
        print(f"\n📋 服务器MR信息:")
        print(f"   地址: {mr_info.get('mr_addr', 'N/A')}")
        print(f"   RKEY: {mr_info.get('rkey', 'N/A')}")
        print(f"   大小: {mr_info.get('size', 'N/A')}")
    
    print("\n🔍 建议:")
    if not tcp_rdma_ok and not tcp_mgmt_ok:
        print("   - 服务器RDMA服务可能未启动或端口4791未监听")
        print("   - 检查服务器防火墙设置")
    elif tcp_mgmt_ok and not tcp_rdma_ok:
        print("   - 服务器在管理网络上监听，但RDMA网络不可达")
        print("   - 建议使用管理网络地址进行RDMA连接")
    elif tcp_rdma_ok:
        print("   - RDMA网络连接正常，可以尝试真实RDMA操作")

if __name__ == "__main__":
    main()
