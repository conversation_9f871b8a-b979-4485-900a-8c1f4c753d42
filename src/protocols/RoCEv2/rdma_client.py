from pyverbs import device, pd, cq, qp, mr, enums
import socket

class RDMAClient:
    def __init__(self, server_ip, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存
        self.buf = bytearray(buf_size)
        self.mr = mr.MR(
            self.pd,
            self.buf,
            len(self.buf),
            enums.IBV_ACCESS_LOCAL_WRITE | enums.IBV_ACCESS_REMOTE_READ | enums.IBV_ACCESS_REMOTE_WRITE
        )
        
        # 4. 连接 TCP 交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((server_ip, port))
        print("[Client] Connected to server")

    def establish_rdma(self):
        # 1. 接收 Server 的 QP 参数
        server_qp_info = eval(self.sock.recv(1024).decode())
        print(f"[Client] Received server QP info: {server_qp_info}")
        
        # 2. 发送 Client 的 QP 参数
        client_qp_info = {
            'qpn': self.qp.qp_num,
            'psn': 0x5678,  # 初始 PSN
            'gid': bytes(16)
        }
        self.sock.sendall(str(client_qp_info).encode())
        
        # 3. 配置 QP 到 RTR
        self.qp.modify(
            qp.QPState.RTR,
            enums.IBV_QP_STATE | enums.IBV_QP_AV | enums.IBV_QP_PATH_MTU |
            enums.IBV_QP_DEST_QPN | enums.IBV_QP_RQ_PSN | enums.IBV_QP_MAX_DEST_RD_ATOMIC,
            qp.QPAttr(
                ah_attr=qp.AHAttr(
                    is_global=0,
                    port_num=1
                ),
                dest_qpn=server_qp_info['qpn'],
                path_mtu=enums.IBV_MTU_1024,
                rq_psn=server_qp_info['psn']
            )
        )
        
        # 4. 配置 QP 到 RTS
        self.qp.modify(
            qp.QPState.RTS,
            enums.IBV_QP_STATE | enums.IBV_QP_SQ_PSN | enums.IBV_QP_TIMEOUT,
            qp.QPAttr(
                sq_psn=client_qp_info['psn'],
                timeout=12
            )
        )
        
        print("[Client] RDMA connection established!")

    def rdma_write(self, data):
        """向 Server 发送 RDMA Write 操作"""
        self.buf[:len(data)] = data.encode()
        self.qp.post_send(
            opcode=enums.IBV_WR_RDMA_WRITE,
            send_wr=qp.SendWR(
                sg=qp.SGE(
                    addr=self.mr.buf_addr(),
                    length=len(data),
                    lkey=self.mr.lkey
                ),
                remote_addr=0,  # Server MR 起始地址（需提前交换）
                rkey=0x1234     # Server MR 的 remote key（需提前交换）
            )
        )
        print(f"[Client] Sent RDMA Write: {data}")

if __name__ == "__main__":
    client = RDMAClient("*************")  # 替换为服务器IP
    client.establish_rdma()
    client.rdma_write("Hello, RDMA!")