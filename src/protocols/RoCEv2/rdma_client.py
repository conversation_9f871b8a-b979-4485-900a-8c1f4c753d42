from pyverbs import device, pd, cq, qp, mr, enums
from pyverbs.addr import AHAttr
import socket

class RDMAClient:
    def __init__(self, server_ip, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存
        self.buf_size = buf_size
        access_flags = (enums.IBV_ACCESS_LOCAL_WRITE |
                       enums.IBV_ACCESS_REMOTE_READ |
                       enums.IBV_ACCESS_REMOTE_WRITE)
        self.mr = mr.MR(self.pd, self.buf_size, access_flags)
        print(f"[Client] MR created: addr={self.mr.buf}, lkey={self.mr.lkey}, rkey={self.mr.rkey}")
        
        # 4. 连接 TCP 交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((server_ip, port))
        print("[Client] Connected to server")

    def establish_rdma(self):
        # 1. 接收 Server 的 QP 参数
        server_qp_info = eval(self.sock.recv(1024).decode())
        print(f"[Client] Received server QP info: {server_qp_info}")
        
        # 2. 获取本地连接参数
        # 尝试获取有效的GID
        gid_obj = None
        gid_str = None
        for gid_index in [1, 0]:  # 先尝试索引1，再尝试索引0
            try:
                gid_obj = self.dev.query_gid(1, gid_index)  # port_num, gid_index
                gid_str = str(gid_obj)  # 转换为字符串
                print(f"[Client] Local GID[{gid_index}]: {gid_str}")
                # 检查是否是有效的非零GID
                if gid_str and gid_str != "0000:0000:0000:0000:0000:0000:0000:0000":
                    print(f"[Client] Using GID index {gid_index}")
                    break
            except Exception as e:
                print(f"[Client] Failed to get GID[{gid_index}]: {e}")
                continue

        if not gid_str or gid_str == "0000:0000:0000:0000:0000:0000:0000:0000":
            # 如果获取失败或全零，使用默认GID
            gid_str = "0000:0000:0000:0000:0000:0000:0000:0000"
            print(f"[Client] Using default GID (all zeros)")

        # 将GID字符串转换为bytes用于传输
        gid_bytes = gid_str.encode('utf-8')

        try:
            # 获取端口属性，包含LID
            port_attr = self.dev.query_port(1)
            lid = port_attr.lid
            print(f"[Client] Local LID: {lid}")
        except:
            # 如果获取失败，使用默认LID
            lid = 2  # 客户端使用不同的LID
            print(f"[Client] Using default LID: {lid}")

        # 发送 Client 的完整 QP 参数
        client_qp_info = {
            'lid': lid,
            'qpn': self.qp.qp_num,
            'psn': 0x5678,  # 初始 PSN
            'gid': gid_bytes  # 使用bytes格式传输
        }
        self.sock.sendall(str(client_qp_info).encode())
        
        # 3. 先配置 QP 到 INIT 状态
        qp_attr_init = qp.QPAttr()
        qp_attr_init.qp_state = enums.IBV_QPS_INIT
        qp_attr_init.pkey_index = 0
        qp_attr_init.port_num = 1
        qp_attr_init.qp_access_flags = (enums.IBV_ACCESS_REMOTE_READ |
                                       enums.IBV_ACCESS_REMOTE_WRITE)

        self.qp.to_init(qp_attr_init)
        print("[Client] QP transitioned to INIT")

        # 4. 配置 QP 到 RTR
        qp_attr = qp.QPAttr()
        qp_attr.qp_state = enums.IBV_QPS_RTR
        qp_attr.path_mtu = enums.IBV_MTU_1024
        qp_attr.dest_qp_num = server_qp_info['qpn']
        qp_attr.rq_psn = server_qp_info['psn']
        qp_attr.max_dest_rd_atomic = 1
        qp_attr.min_rnr_timer = 12

        # 配置AH属性（尝试全局路由用于RoCEv2）
        ah_attr = AHAttr(port_num=1)

        # 处理服务器GID
        server_gid_data = server_qp_info['gid']
        if isinstance(server_gid_data, bytes):
            try:
                server_gid_str = server_gid_data.decode('utf-8')
                print(f"[Client] Server GID: {server_gid_str}")
            except:
                server_gid_str = "0000:0000:0000:0000:0000:0000:0000:0000"
                print(f"[Client] Using default server GID: {server_gid_str}")
        else:
            server_gid_str = str(server_gid_data)
            print(f"[Client] Server GID: {server_gid_str}")

        # 尝试本地路由（软件RoCEv2可能更适合）
        ah_attr.is_global = 0  # 禁用全局路由
        ah_attr.dlid = server_qp_info.get('lid', 1)  # 使用服务器LID
        ah_attr.sl = 0

        qp_attr.ah_attr = ah_attr
        print(f"[Client] Using global routing with server GID")

        self.qp.to_rtr(qp_attr)
        
        # 4. 配置 QP 到 RTS
        qp_attr_rts = qp.QPAttr()
        qp_attr_rts.qp_state = enums.IBV_QPS_RTS
        qp_attr_rts.timeout = 12
        qp_attr_rts.retry_cnt = 7
        qp_attr_rts.rnr_retry = 7
        qp_attr_rts.sq_psn = client_qp_info['psn']
        qp_attr_rts.max_rd_atomic = 1

        self.qp.to_rts(qp_attr_rts)
        
        print("[Client] RDMA connection established!")

    def rdma_write(self, data, remote_addr=0, remote_rkey=0):
        """向 Server 发送 RDMA Write 操作"""
        # 将数据写入本地MR
        data_bytes = data.encode() if isinstance(data, str) else data
        self.mr.write(data_bytes, len(data_bytes), 0)

        # 创建SGE
        from pyverbs.wr import SGE, SendWR
        sge = SGE(addr=self.mr.buf, length=len(data_bytes), lkey=self.mr.lkey)

        # 创建发送工作请求
        send_wr = SendWR(opcode=enums.IBV_WR_RDMA_WRITE, num_sge=1, sg=[sge])
        send_wr.set_wr_rdma(rkey=remote_rkey, addr=remote_addr)

        # 发送
        self.qp.post_send(send_wr)
        print(f"[Client] Sent RDMA Write: {data}")

if __name__ == "__main__":
    client = RDMAClient("127.0.0.1")  # 本地测试
    client.establish_rdma()
    client.rdma_write("Hello, RDMA!")