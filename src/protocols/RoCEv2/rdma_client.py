from pyverbs import device, pd, cq, qp, mr, enums
from pyverbs.addr import AHAttr
import socket

class RDMAClient:
    def __init__(self, server_ip, port=12345, buf_size=1024):
        # 1. 初始化 RDMA 设备
        devices = device.get_device_list()
        if not devices:
            raise RuntimeError("No RDMA devices found!")
        self.dev = device.Context(name=devices[0].name.decode())
        self.pd = pd.PD(self.dev)
        self.cq = cq.CQ(self.dev, cqe=10)
        
        # 2. 创建 QP
        qp_init_attr = qp.QPInitAttr(
            qp_type=enums.IBV_QPT_RC,
            scq=self.cq,
            rcq=self.cq,
            cap=qp.QPCap(max_send_wr=10, max_recv_wr=10)
        )
        self.qp = qp.QP(self.pd, qp_init_attr)
        
        # 3. 注册内存
        self.buf_size = buf_size
        access_flags = (enums.IBV_ACCESS_LOCAL_WRITE |
                       enums.IBV_ACCESS_REMOTE_READ |
                       enums.IBV_ACCESS_REMOTE_WRITE)
        self.mr = mr.MR(self.pd, self.buf_size, access_flags)
        print(f"[Client] MR created: addr={self.mr.buf}, lkey={self.mr.lkey}, rkey={self.mr.rkey}")
        
        # 4. 连接 TCP 交换 QP 参数
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((server_ip, port))
        print("[Client] Connected to server")

    def establish_rdma(self):
        # 1. 接收 Server 的 QP 参数
        server_qp_info = eval(self.sock.recv(1024).decode())
        print(f"[Client] Received server QP info: {server_qp_info}")
        
        # 2. 发送 Client 的 QP 参数
        client_qp_info = {
            'qpn': self.qp.qp_num,
            'psn': 0x5678,  # 初始 PSN
            # 'gid': bytes(16)
            'gid': b'::ffff:**********'
        }
        self.sock.sendall(str(client_qp_info).encode())
        
        # 3. 配置 QP 到 RTR
        qp_attr = qp.QPAttr()
        qp_attr.qp_state = enums.IBV_QPS_RTR
        qp_attr.path_mtu = enums.IBV_MTU_1024
        qp_attr.dest_qp_num = server_qp_info['qpn']
        qp_attr.rq_psn = server_qp_info['psn']
        qp_attr.max_dest_rd_atomic = 1
        qp_attr.min_rnr_timer = 12
        qp_attr.ah_attr = AHAttr(port_num=1)

        self.qp.to_rtr(qp_attr)
        
        # 4. 配置 QP 到 RTS
        qp_attr_rts = qp.QPAttr()
        qp_attr_rts.qp_state = enums.IBV_QPS_RTS
        qp_attr_rts.timeout = 12
        qp_attr_rts.retry_cnt = 7
        qp_attr_rts.rnr_retry = 7
        qp_attr_rts.sq_psn = client_qp_info['psn']
        qp_attr_rts.max_rd_atomic = 1

        self.qp.to_rts(qp_attr_rts)
        
        print("[Client] RDMA connection established!")

    def rdma_write(self, data, remote_addr=0, remote_rkey=0):
        """向 Server 发送 RDMA Write 操作"""
        # 将数据写入本地MR
        data_bytes = data.encode() if isinstance(data, str) else data
        self.mr.write(data_bytes, len(data_bytes), 0)

        # 创建SGE
        from pyverbs.wr import SGE, SendWR
        sge = SGE(addr=self.mr.buf, length=len(data_bytes), lkey=self.mr.lkey)

        # 创建发送工作请求
        send_wr = SendWR(opcode=enums.IBV_WR_RDMA_WRITE, num_sge=1, sg=[sge])
        send_wr.set_wr_rdma(rkey=remote_rkey, addr=remote_addr)

        # 发送
        self.qp.post_send(send_wr)
        print(f"[Client] Sent RDMA Write: {data}")

if __name__ == "__main__":
    client = RDMAClient("127.0.0.1")  # 本地测试
    client.establish_rdma()
    client.rdma_write("Hello, RDMA!")