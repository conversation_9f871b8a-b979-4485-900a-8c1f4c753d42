from ctypes import *
import socket
import struct

# 加载 librdmacm
try:
    lib = CDLL("/lib/x86_64-linux-gnu/librdmacm.so")
except OSError as e:
    raise RuntimeError(f"Failed to load librdmacm: {e}")

# 定义 RDMA CM 事件类型
class rdma_cm_event(Structure):
    _fields_ = [
        ("id", c_void_p),
        ("listen_id", c_void_p),
        ("event", c_int),
        ("status", c_int),
        ("private_data", c_void_p),
        ("private_data_len", c_uint8),
    ]

# 初始化 RDMA CM
def create_rdma_connection(server_ip, port=7174):
    # 1. 创建 RDMA 事件通道
    channel = lib.rdma_create_event_channel()
    if not channel:
        raise RuntimeError("Failed to create RDMA event channel")

    # 2. 创建 RDMA CM ID（注意：传递正确的参数）
    cm_id = c_void_p()
    ret = lib.rdma_create_id(channel, byref(cm_id), None, 0)  # 最后一个参数是 "RDMA_PS_TCP"（0）
    if ret != 0:
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_create_id failed with error {ret}")

    # 3. 解析目标地址（rping 默认 7174 或 RoCEv2 4791）
    # 构造 sockaddr_in
    addr = socket.inet_aton(server_ip)
    port = socket.htons(port)
    family = socket.AF_INET
    sockaddr = struct.pack("!HH4s8s", family, port, addr, b"\x00" * 8)

    # 4. 解析 RDMA 地址
    ret = lib.rdma_resolve_addr(
        cm_id,
        None,                      # src_addr (None = 用默认地址)
        cast(sockaddr, c_void_p),  # 目标地址
        2000,                      # 超时（毫秒）
    )
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_resolve_addr failed with error {ret}")

    # 5. 等待地址解析完成
    event = rdma_cm_event()
    ret = lib.rdma_get_cm_event(channel, byref(event))
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    if event.event != 1:  # RDMA_CM_EVENT_ADDR_RESOLVED (1)
        lib.rdma_ack_cm_event(event)
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event {event.event}")

    lib.rdma_ack_cm_event(event)

    # 6. 解析路由
    ret = lib.rdma_resolve_route(cm_id, 2000)  # 超时 2000ms
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_resolve_route failed with error {ret}")

    # 7. 等待路由解析完成
    ret = lib.rdma_get_cm_event(channel, byref(event))
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    if event.event != 2:  # RDMA_CM_EVENT_ROUTE_RESOLVED (2)
        lib.rdma_ack_cm_event(event)
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event {event.event}")

    lib.rdma_ack_cm_event(event)

    # 8. 发送连接请求（模拟 rping 客户端）
    ret = lib.rdma_connect(cm_id, None)  # NULL conn_param
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_connect failed with error {ret}")

    # 9. 等待连接建立
    ret = lib.rdma_get_cm_event(channel, byref(event))
    if ret != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    if event.event == 4:  # RDMA_CM_EVENT_CONNECT_RESPONSE (4)
        print("[Success] RDMA connection established!")
    else:
        print(f"[Error] Unexpected CM event: {event.event}")

    # 10. 清理资源
    lib.rdma_ack_cm_event(event)
    lib.rdma_disconnect(cm_id)
    lib.rdma_destroy_id(cm_id)
    lib.rdma_destroy_event_channel(channel)

# 调用测试
try:
    create_rdma_connection("**********", 7174)  # 替换为你的服务器IP
except Exception as e:
    print(f"[Error] {e}")
