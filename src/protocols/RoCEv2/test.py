import socket
import json
from pyverbs.mr import MR


class RDMAClient:
    def __init__(self, server_ip):
        self.server_ip = server_ip
        self._fetch_mr_info()  # 通过UDP获取MR信息

    def _fetch_mr_info(self):
        """从Server的UDP接口获取MR地址和rkey"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(b"GET_MR", (self.server_ip, 8888))
        reply = sock.recv(1024).decode('utf-8')
        # 解析回复示例: "{'MR_ADDR': '0x1000', 'RKEY'=1234}"
        mr_info = json.loads(reply)
        self.server_mr_addr = int(mr_info["mr_addr"], 16)
        self.server_rkey = mr_info["rkey"]

    def validate_memory(self):
        """验证Server内存读写"""
        test_data = b"TEST_PATTERN"
        self.mr.write(test_data, len(test_data), self.server_mr_addr, self.server_rkey)
        read_back = self.mr.read(len(test_data), self.server_mr_addr, self.server_rkey)
        assert test_data == read_back

# def get_mr_info(server_ip, port=8888):
#     sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
#     sock.sendto(b"GET_MR", (server_ip, port))
#     reply = sock.recv(256).decode('utf-8')
#     print(f"原始响应: {reply}")  # 调试输出
    
#     mr_info = json.loads(reply)  # 自动解析JSON
    
#     # 转换地址为整数（去掉前缀"0x"）
#     mr_addr = int(mr_info["mr_addr"], 16)
#     rkey = mr_info["rkey"]
    
#     return mr_addr, rkey

# mr_addr, rkey = get_mr_info("***********")
# print(f"MR地址: {hex(mr_addr)}, RKEY: {rkey}")
if __name__ == "__main__":
    client = RDMAClient("***********")
    client.validate_memory()
