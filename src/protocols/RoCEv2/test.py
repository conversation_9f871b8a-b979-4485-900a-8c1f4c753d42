import socket
import json


def get_mr_info(server_ip, port=8888):
    """从Server的UDP接口获取MR地址和rkey"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.sendto(b"GET_MR", (server_ip, port))
    reply = sock.recv(1024).decode('utf-8')
    print(f"原始响应: {reply}")  # 调试输出

    mr_info = json.loads(reply)  # 自动解析JSON

    # 转换地址为整数（去掉前缀"0x"）
    mr_addr = int(mr_info["mr_addr"], 16)
    rkey = mr_info["rkey"]

    return mr_addr, rkey


def test_basic_pyverbs():
    """测试基本的pyverbs功能"""
    try:
        from pyverbs.device import get_device_list
        dev_list = get_device_list()
        if not dev_list:
            print("❌ 未找到可用的RDMA设备")
            return False
        print(f"✅ 可用RDMA设备: {[dev.name.decode('utf-8') for dev in dev_list]}")
        return True
    except Exception as e:
        print(f"❌ pyverbs测试失败: {e}")
        return False

if __name__ == "__main__":
    # 首先测试基本功能
    if test_basic_pyverbs():
        print("✅ pyverbs基本功能正常")
    else:
        print("❌ pyverbs基本功能异常")
        exit(1)

    # 测试UDP通信
    try:
        mr_addr, rkey = get_mr_info("10.61.72.27")
        print(f"✅ 成功获取MR信息 - 地址: {hex(mr_addr)}, RKEY: {rkey}")
    except Exception as e:
        print(f"❌ 获取MR信息失败: {e}")
