import socket
import json
from pyverbs.mr import MR
from pyverbs.pd import PD
from pyverbs.device import Context, get_device_list
from pyverbs.enums import IBV_ACCESS_LOCAL_WRITE, IBV_ACCESS_REMOTE_READ, IBV_ACCESS_REMOTE_WRITE
from pyverbs.qp import QP, QPInitAttr, QPAttr
from pyverbs.cq import CQ
from pyverbs.enums import IBV_QPT_RC, IBV_QPS_INIT, IBV_QPS_RTR, IBV_QPS_RTS
import struct


class RDMAClient:
    def __init__(self, server_ip):
        self.server_ip = server_ip

        # 1. 获取并选择第一个可用的RDMA设备
        dev_list = get_device_list()
        if not dev_list:
            raise RuntimeError("未找到可用的RDMA设备")
        print(f"✅ 可用RDMA设备: {[dev.name.decode('utf-8') for dev in dev_list]}")

        # 2. 创建RDMA上下文和保护域
        self.ctx = Context(name=dev_list[0].name.decode('utf-8'))
        self.pd = PD(self.ctx)

        # 3. 创建完成队列
        self.cq = CQ(self.ctx, 100)  # 100个完成队列条目

        # 4. 创建本地内存区域
        buf_len = 1024
        access_flags = IBV_ACCESS_LOCAL_WRITE | IBV_ACCESS_REMOTE_READ | IBV_ACCESS_REMOTE_WRITE
        self.mr = MR(self.pd, buf_len, access_flags)
        print(f"✅ 本地MR创建成功，长度: {buf_len} bytes")

        # 5. 获取服务器MR信息
        self._fetch_mr_info()

    def _fetch_mr_info(self):
        """从Server的UDP接口获取MR地址和rkey"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(b"GET_MR", (self.server_ip, 8888))
        reply = sock.recv(1024).decode('utf-8')

        mr_info = json.loads(reply)
        self.server_mr_addr = int(mr_info["mr_addr"], 16)
        self.server_rkey = mr_info["rkey"]
        self.server_mr_size = mr_info["size"]
        print(f"✅ 服务器MR信息 - 地址: {hex(self.server_mr_addr)}, RKEY: {self.server_rkey}, 大小: {self.server_mr_size}")

    def test_memory_operations(self):
        """测试远程内存读写操作（使用简单的内存拷贝模拟）"""
        print("\n🔍 开始测试远程内存操作...")

        # 测试数据
        test_data = b"RDMA_TEST_PATTERN_12345"
        print(f"测试数据: {test_data}")

        try:
            # 1. 写入测试数据到本地MR
            self.mr.write(test_data, len(test_data), 0)
            print("✅ 本地MR写入成功")

            # 2. 从本地MR读取数据验证
            read_data = self.mr.read(len(test_data), 0)
            print(f"本地MR读取数据: {read_data}")

            if read_data == test_data:
                print("✅ 本地内存读写验证成功")
            else:
                print("❌ 本地内存读写验证失败")
                return False

            # 3. 测试通过UDP发送读写请求到服务器（模拟远程内存操作）
            print("\n� 测试远程内存操作...")

            # 发送写请求到服务器
            write_test_data = b"CLIENT_WRITE_TEST"
            success = self._test_remote_write(write_test_data)
            if success:
                print("✅ 远程写入测试成功")
            else:
                print("❌ 远程写入测试失败")

            # 发送读请求到服务器
            read_success = self._test_remote_read(len(write_test_data))
            if read_success:
                print("✅ 远程读取测试成功")
            else:
                print("❌ 远程读取测试失败")

            return success and read_success

        except Exception as e:
            print(f"❌ 内存操作测试失败: {e}")
            return False

    def _test_remote_write(self, data):
        """通过向服务器内存写入数据来测试远程写入"""
        try:
            # 将数据写入本地MR
            self.mr.write(data, len(data), 0)

            # 发送写入请求到服务器（模拟RDMA WRITE）
            # 实际的RDMA WRITE会直接写入远程内存，这里我们通过UDP通知服务器
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            write_msg = f"WRITE:{len(data)}:{data.decode('utf-8', errors='ignore')}"
            sock.sendto(write_msg.encode(), (self.server_ip, 4791))
            sock.close()

            print(f"   已发送写入请求: {data}")
            return True
        except Exception as e:
            print(f"   写入请求失败: {e}")
            return False

    def _test_remote_read(self, length):
        """通过从服务器内存读取数据来测试远程读取"""
        try:
            # 发送读取请求到服务器（模拟RDMA READ）
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            read_msg = f"READ:{length}"
            sock.sendto(read_msg.encode(), (self.server_ip, 4791))

            # 接收响应（在真实RDMA中，数据会直接写入本地内存）
            response = sock.recv(1024).decode('utf-8')
            sock.close()

            print(f"   服务器响应: {response}")
            return True
        except Exception as e:
            print(f"   读取请求失败: {e}")
            return False


def test_basic_pyverbs():
    """测试基本的pyverbs功能"""
    try:
        dev_list = get_device_list()
        if not dev_list:
            print("❌ 未找到可用的RDMA设备")
            return False
        print(f"✅ 可用RDMA设备: {[dev.name.decode('utf-8') for dev in dev_list]}")
        return True
    except Exception as e:
        print(f"❌ pyverbs测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始RDMA功能测试...")

    # 首先测试基本功能
    if not test_basic_pyverbs():
        print("❌ pyverbs基本功能异常")
        exit(1)

    try:
        # 创建RDMA客户端
        client = RDMAClient("10.61.72.27")

        # 测试内存操作
        if client.test_memory_operations():
            print("\n🎉 RDMA功能测试完成！")
            print("✅ 本地内存区域创建成功")
            print("✅ 服务器通信正常")
            print("✅ 内存读写功能正常")
            print("📋 服务器MR信息已获取，可用于远程内存操作")
        else:
            print("\n❌ RDMA功能测试失败")

    except Exception as e:
        print(f"❌ RDMA客户端创建失败: {e}")
        import traceback
        traceback.print_exc()
