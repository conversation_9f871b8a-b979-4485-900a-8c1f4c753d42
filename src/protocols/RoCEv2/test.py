import socket
import json
import ctypes
from pyverbs.mr import MR
from pyverbs.pd import PD
from pyverbs.device import Context, get_device_list



class RDMAClient:
    def __init__(self, server_ip):
        self.server_ip = server_ip
        
        # 1. 获取并选择第一个可用的RDMA设备
        dev_list = get_device_list()
        if not dev_list:
            raise RuntimeError("未找到可用的RDMA设备")
        print(f"可用RDMA设备: {[dev.name.decode('utf-8') for dev in dev_list]}")
        
        # 使用第一个设备创建上下文
        self.ctx = Context(name=dev_list[0].name.decode('utf-8'))  
        self.pd = PD(self.ctx)
        
        # 2. 创建本地MR缓冲区
        # self.mr = MR(self.pd, 1024, 
        #             access=0b111)  # 直接指定缓冲区大小而不是传递buffer
        # self.local_mem = self.mr.buf  # 获取MR分配的内存缓冲区
        self.local_buf = bytearray(b"hello world")
        buf_ptr = ctypes.cast(ctypes.pointer(ctypes.c_char.from_buffer(self.local_buf)), ctypes.c_void_p).value
        buf_len = len(self.local_buf)
        self.mr = MR(self.pd, self.local_buf, access=0b111) 
        self.mr = MR(pd, buf_ptr, buf_len, access=0b111)

        self._fetch_mr_info()  # 通过UDP获取MR信息

    def _fetch_mr_info(self):
        """从Server的UDP接口获取MR地址和rkey"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(b"GET_MR", (self.server_ip, 8888))
        reply = sock.recv(1024).decode('utf-8')
        # 解析回复示例: "{'MR_ADDR': '0x1000', 'RKEY'=1234}"
        mr_info = json.loads(reply)
        self.server_mr_addr = int(mr_info["mr_addr"], 16)
        self.server_rkey = mr_info["rkey"]

    def validate_memory(self):
        """验证Server内存读写"""
        test_data = b"TEST_PATTERN"
        
        # 1. 写入数据到服务器内存
        self.mr.write(self.local_buf, self.server_mr_addr)
        print(f"已写入服务器内存: {self.local_buf}")
        
        # 2. 从服务器内存读取数据
        read_back = self.mr.read(len(self.local_buf), self.server_mr_addr)
        print(f"读取到服务器内存: {read_back}")
        
        # 3. 验证数据一致性
        if test_data == read_back:
            print("✅ 内存验证成功")
        else:
            print("❌ 内存验证失败")
            raise ValueError("Server memory validation failed")

if __name__ == "__main__":
    client = RDMAClient("10.61.72.27")
    client.validate_memory()
