from ctypes import *
import socket
import struct

# 加载 librdmacm
try:
    lib = CDLL("/lib/x86_64-linux-gnu/librdmacm.so")
except OSError as e:
    raise RuntimeError(f"Failed to load librdmacm: {e}")

# 定义函数签名
# rdma_create_event_channel() -> rdma_event_channel*
lib.rdma_create_event_channel.argtypes = []
lib.rdma_create_event_channel.restype = c_void_p

# rdma_create_id(channel, id, context, ps) -> int
lib.rdma_create_id.argtypes = [c_void_p, POINTER(c_void_p), c_void_p, c_int]
lib.rdma_create_id.restype = c_int

# rdma_destroy_event_channel(channel) -> void
lib.rdma_destroy_event_channel.argtypes = [c_void_p]
lib.rdma_destroy_event_channel.restype = None

# rdma_destroy_id(id) -> int
lib.rdma_destroy_id.argtypes = [c_void_p]
lib.rdma_destroy_id.restype = c_int

# rdma_resolve_addr(id, src_addr, dst_addr, timeout_ms) -> int
lib.rdma_resolve_addr.argtypes = [c_void_p, c_void_p, c_void_p, c_int]
lib.rdma_resolve_addr.restype = c_int

# rdma_resolve_route(id, timeout_ms) -> int
lib.rdma_resolve_route.argtypes = [c_void_p, c_int]
lib.rdma_resolve_route.restype = c_int

# rdma_connect(id, conn_param) -> int
lib.rdma_connect.argtypes = [c_void_p, c_void_p]
lib.rdma_connect.restype = c_int

# rdma_disconnect(id) -> int
lib.rdma_disconnect.argtypes = [c_void_p]
lib.rdma_disconnect.restype = c_int

# rdma_get_cm_event(channel, event) -> int
lib.rdma_get_cm_event.argtypes = [c_void_p, POINTER(c_void_p)]
lib.rdma_get_cm_event.restype = c_int

# rdma_ack_cm_event(event) -> int
lib.rdma_ack_cm_event.argtypes = [c_void_p]
lib.rdma_ack_cm_event.restype = c_int

# 定义 RDMA CM 事件类型
class rdma_cm_event(Structure):
    _fields_ = [
        ("id", c_void_p),
        ("listen_id", c_void_p),
        ("event", c_int),
        ("status", c_int),
        ("private_data", c_void_p),
        ("private_data_len", c_uint8),
    ]

# 定义常量
RDMA_PS_TCP = 0x0106  # 正确的RDMA_PS_TCP值

# 定义sockaddr_in结构
class sockaddr_in(Structure):
    _fields_ = [
        ("sin_family", c_uint16),
        ("sin_port", c_uint16),
        ("sin_addr", c_uint32),
        ("sin_zero", c_uint8 * 8),
    ]

# 初始化 RDMA CM
def create_rdma_connection(server_ip, port=7174):
    # 1. 创建 RDMA 事件通道
    channel = lib.rdma_create_event_channel()
    if not channel:
        raise RuntimeError("Failed to create RDMA event channel")

    # 2. 创建 RDMA CM ID
    cm_id = c_void_p()
    ret = lib.rdma_create_id(channel, byref(cm_id), None, RDMA_PS_TCP)
    if ret != 0:
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_create_id failed with error {ret}")

    # 3. 构造目标地址结构
    dst_addr = sockaddr_in()
    dst_addr.sin_family = socket.AF_INET
    dst_addr.sin_port = socket.htons(port)
    dst_addr.sin_addr = struct.unpack("!I", socket.inet_aton(server_ip))[0]

    print(f"🔗 尝试连接到 {server_ip}:{port}")

    # 4. 解析 RDMA 地址
    ret = lib.rdma_resolve_addr(
        cm_id.value,               # 使用.value获取实际指针值
        None,                      # src_addr (None = 用默认地址)
        byref(dst_addr),           # 目标地址
        2000,                      # 超时（毫秒）
    )
    if ret != 0:
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_resolve_addr failed with error {ret}")

    # 5. 等待地址解析完成
    event_ptr = c_void_p()
    ret = lib.rdma_get_cm_event(channel, byref(event_ptr))
    if ret != 0:
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    # 将指针转换为结构体
    event = cast(event_ptr, POINTER(rdma_cm_event)).contents

    print(f"📨 收到RDMA事件: {event.event}")

    if event.event != 1:  # RDMA_CM_EVENT_ADDR_RESOLVED (1)
        lib.rdma_ack_cm_event(event_ptr)
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event {event.event}, expected ADDR_RESOLVED(1)")

    lib.rdma_ack_cm_event(event_ptr)

    # 6. 解析路由
    print("🛣️  解析路由...")
    print(f"   CM ID: {cm_id}")
    try:
        ret = lib.rdma_resolve_route(cm_id.value, 2000)  # 超时 2000ms
        print(f"   rdma_resolve_route返回: {ret}")
        if ret != 0:
            lib.rdma_destroy_id(cm_id.value)
            lib.rdma_destroy_event_channel(channel)
            raise RuntimeError(f"rdma_resolve_route failed with error {ret}")
    except Exception as e:
        print(f"   ❌ rdma_resolve_route异常: {e}")
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise

    # 7. 等待路由解析完成
    event_ptr2 = c_void_p()
    ret = lib.rdma_get_cm_event(channel, byref(event_ptr2))
    if ret != 0:
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    event2 = cast(event_ptr2, POINTER(rdma_cm_event)).contents
    print(f"📨 收到路由事件: {event2.event}")

    if event2.event != 2:  # RDMA_CM_EVENT_ROUTE_RESOLVED (2)
        lib.rdma_ack_cm_event(event_ptr2)
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event {event2.event}, expected ROUTE_RESOLVED(2)")

    lib.rdma_ack_cm_event(event_ptr2)

    # 8. 发送连接请求（模拟 rping 客户端）
    ret = lib.rdma_connect(cm_id.value, None)  # NULL conn_param
    if ret != 0:
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_connect failed with error {ret}")

    # 9. 等待连接建立
    event_ptr3 = c_void_p()
    ret = lib.rdma_get_cm_event(channel, byref(event_ptr3))
    if ret != 0:
        lib.rdma_destroy_id(cm_id.value)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_get_cm_event failed with error {ret}")

    event3 = cast(event_ptr3, POINTER(rdma_cm_event)).contents
    if event3.event == 4:  # RDMA_CM_EVENT_CONNECT_RESPONSE (4)
        print("🎉 [Success] RDMA connection established!")
    else:
        print(f"❌ [Error] Unexpected CM event: {event3.event}")

    # 10. 清理资源
    lib.rdma_ack_cm_event(event_ptr3)
    lib.rdma_disconnect(cm_id.value)
    lib.rdma_destroy_id(cm_id.value)
    lib.rdma_destroy_event_channel(channel)

# 调用测试
try:
    create_rdma_connection("**********")  # 替换为你的服务器IP
except Exception as e:
    print(f"[Error] {e}")
