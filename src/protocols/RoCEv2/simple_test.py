#!/usr/bin/env python3
"""
简化的RDMA连接测试
"""

from ctypes import *
import socket

# 加载库
lib = CDLL("/lib/x86_64-linux-gnu/librdmacm.so")

# 定义函数签名
lib.rdma_create_event_channel.argtypes = []
lib.rdma_create_event_channel.restype = c_void_p

lib.rdma_create_id.argtypes = [c_void_p, POINTER(c_void_p), c_void_p, c_int]
lib.rdma_create_id.restype = c_int

lib.rdma_destroy_id.argtypes = [c_void_p]
lib.rdma_destroy_id.restype = c_int

lib.rdma_destroy_event_channel.argtypes = [c_void_p]
lib.rdma_destroy_event_channel.restype = None

# 定义sockaddr_in结构
class sockaddr_in(Structure):
    _fields_ = [
        ("sin_family", c_uint16),
        ("sin_port", c_uint16),
        ("sin_addr", c_uint32),
        ("sin_zero", c_uint8 * 8),
    ]

def test_basic_rdma():
    """测试基本的RDMA组件创建"""
    print("🔍 测试基本RDMA组件...")
    
    # 1. 创建事件通道
    print("1️⃣ 创建事件通道...")
    channel = lib.rdma_create_event_channel()
    if not channel:
        print("❌ 创建事件通道失败")
        return False
    print("✅ 事件通道创建成功")
    
    # 2. 创建CM ID
    print("2️⃣ 创建CM ID...")
    cm_id = c_void_p()
    ret = lib.rdma_create_id(channel, byref(cm_id), None, 0x0106)  # RDMA_PS_TCP
    if ret != 0:
        print(f"❌ 创建CM ID失败: {ret}")
        lib.rdma_destroy_event_channel(channel)
        return False
    print("✅ CM ID创建成功")
    
    # 3. 清理
    print("3️⃣ 清理资源...")
    lib.rdma_destroy_id(cm_id.value)
    lib.rdma_destroy_event_channel(channel)
    print("✅ 清理完成")
    
    return True

def test_server_connectivity():
    """测试服务器连通性"""
    print("\n🔍 测试服务器连通性...")
    
    # 测试UDP端口8888
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2)
        sock.sendto(b"GET_MR", ("10.61.72.27", 8888))
        response = sock.recv(1024)
        sock.close()
        print("✅ UDP端口8888可达")
        print(f"   响应: {response.decode('utf-8')}")
        return True
    except Exception as e:
        print(f"❌ UDP端口8888不可达: {e}")
        return False

if __name__ == "__main__":
    print("🚀 简化RDMA测试")
    print("=" * 40)
    
    # 测试基本组件
    basic_ok = test_basic_rdma()
    
    # 测试服务器连通性
    server_ok = test_server_connectivity()
    
    print("\n📋 测试结果:")
    print(f"   基本RDMA组件: {'✅' if basic_ok else '❌'}")
    print(f"   服务器连通性: {'✅' if server_ok else '❌'}")
    
    if basic_ok and server_ok:
        print("\n🎯 结论: RDMA基础功能正常，服务器可达")
        print("💡 建议: 检查服务器端RDMA监听实现")
    else:
        print("\n❌ 存在基础问题，需要先解决")
