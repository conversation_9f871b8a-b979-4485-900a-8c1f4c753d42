from ctypes import *
import socket
import struct

# 加载 librdmacm
lib = CDLL("librdmacm.so")

# RDMA CM 事件类型
class rdma_cm_event(Structure):
    _fields_ = [
        ("id", c_void_p),
        ("listen_id", c_void_p),
        ("event", c_int),
        ("status", c_int),
        ("private_data", c_void_p),
        ("private_data_len", c_uint8),
    ]

def rdma_connect(server_ip):
    # 1. 创建事件通道
    channel = lib.rdma_create_event_channel()
    if not channel:
        raise RuntimeError("rdma_create_event_channel failed")

    # 2. 创建 CM ID（类型 RDMA_PS_TCP）
    cm_id = c_void_p()
    ret = lib.rdma_create_id(channel, byref(cm_id), None, 0)  # 0 = RDMA_PS_TCP
    if ret != 0:
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"rdma_create_id failed with error {ret}")

    # 3. 解析目标地址（不指定端口，让 librdmacm 自动选择）
    addr = (socket.AF_INET, server_ip.encode(), 0, 0, 0)  # port=0 表示动态协商
    if lib.rdma_resolve_addr(cm_id, None, cast(addr, c_void_p), 2000) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_resolve_addr failed")

    # 4. 等待地址解析完成
    event = rdma_cm_event()
    if lib.rdma_get_cm_event(channel, byref(event)) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_get_cm_event (ADDR_RESOLVED) failed")
    
    if event.event != 1:  # 检查是否是 RDMA_CM_EVENT_ADDR_RESOLVED
        lib.rdma_ack_cm_event(event)
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event: {event.event}")
    
    lib.rdma_ack_cm_event(event)

    # 5. 解析路由（自动协商端口）
    if lib.rdma_resolve_route(cm_id, 2000) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_resolve_route failed")

    # 6. 等待路由解析完成
    if lib.rdma_get_cm_event(channel, byref(event)) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_get_cm_event (ROUTE_RESOLVED) failed")
    
    if event.event != 2:  # 检查是否是 RDMA_CM_EVENT_ROUTE_RESOLVED
        lib.rdma_ack_cm_event(event)
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError(f"Unexpected CM event: {event.event}")
    
    lib.rdma_ack_cm_event(event)

    # 7. 发起连接请求（端口已由 CM 协商完成）
    if lib.rdma_connect(cm_id, None) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_connect failed")

    # 8. 等待连接建立（RTU）
    if lib.rdma_get_cm_event(channel, byref(event)) != 0:
        lib.rdma_destroy_id(cm_id)
        lib.rdma_destroy_event_channel(channel)
        raise RuntimeError("rdma_get_cm_event (ESTABLISHED) failed")

    if event.event == 4:  # RDMA_CM_EVENT_ESTABLISHED
        print("[SUCCESS] RDMA connection established!")
    else:
        print(f"[ERROR] Unexpected CM event: {event.event}")

    # 9. 清理资源
    lib.rdma_ack_cm_event(event)
    lib.rdma_disconnect(cm_id)
    lib.rdma_destroy_id(cm_id)
    lib.rdma_destroy_event_channel(channel)

# 测试
try:
    rdma_connect("**********")  # 替换为服务器IP（无需端口）
except Exception as e:
    print(f"[ERROR] {e}")