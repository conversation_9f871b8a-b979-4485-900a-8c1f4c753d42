#include <sys/socket.h>
#include <netinet/in.h>
#include <rdma/rdma_cma.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <time.h>
#include <ctype.h>
#include <stdlib.h>
#include <cjson/cJSON.h>

#define UDP_PORT 8888
#define SHARED_MEM_SIZE 1024
#define RDMA_PORT 4791

char shared_mem[SHARED_MEM_SIZE];
char last_mem[SHARED_MEM_SIZE];
struct ibv_mr *mr;
struct ibv_context *ctx;
struct ibv_pd *pd;
pthread_mutex_t mem_mutex = PTHREAD_MUTEX_INITIALIZER;

void get_time_str(char *buf, size_t len) {
    time_t now = time(NULL);
    struct tm *tm = localtime(&now);
    strftime(buf, len, "%Y-%m-%d %H:%M:%S", tm);
}

void log_mem_change(const char *action, int offset, const void *data, int len) {
    char timestamp[32];
    get_time_str(timestamp, sizeof(timestamp));
    
    printf("[%s] %s at offset %d, %d bytes: 0x", timestamp, action, offset, len);
    const unsigned char *p = data;
    for (int i = 0; i < (len > 16 ? 16 : len); i++) {
        printf("%02x", p[i]);
    }
    if (len > 16) printf("...");
    printf("\n");
    
    printf("    ASCII: \"");
    for (int i = 0; i < (len > 32 ? 32 : len); i++) {
        if (isprint(p[i])) putchar(p[i]);
        else putchar('.');
    }
    printf("\"\n");
}

// 使用cJSON构建JSON响应
void build_json_response(char *reply, size_t len, struct ibv_mr *mr) {
    cJSON *root = cJSON_CreateObject();
    char addr_str[20];
    snprintf(addr_str, sizeof(addr_str), "%p", mr->addr);
    cJSON_AddStringToObject(root, "mr_addr", addr_str);
    cJSON_AddNumberToObject(root, "rkey", mr->rkey);
    cJSON_AddNumberToObject(root, "size", SHARED_MEM_SIZE);
    
    char *json_str = cJSON_PrintUnformatted(root);
    snprintf(reply, len, "%s", json_str);
    free(json_str);
    cJSON_Delete(root);
}

void check_mem_changes() {
    pthread_mutex_lock(&mem_mutex);
    
    if (memcmp(shared_mem, last_mem, SHARED_MEM_SIZE) != 0) {
        for (int i = 0; i < SHARED_MEM_SIZE; i++) {
            if (shared_mem[i] != last_mem[i]) {
                int j = i;
                while (j < SHARED_MEM_SIZE && shared_mem[j] != last_mem[j]) j++;
                log_mem_change("WRITE", i, &shared_mem[i], j - i);
                i = j;
            }
        }
        
        if (memcmp(shared_mem, "READ_REQ_", 9) == 0) {
            int offset = atoi(shared_mem + 9);
            int length = atoi(shared_mem + 13);
            if (offset >= 0 && offset + length <= SHARED_MEM_SIZE) {
                log_mem_change("READ", offset, &shared_mem[offset], length);
            }
            memset(shared_mem, 0, SHARED_MEM_SIZE);
        }
        
        memcpy(last_mem, shared_mem, SHARED_MEM_SIZE);
    }
    
    pthread_mutex_unlock(&mem_mutex);
}

void *start_udp_server(void *arg) {
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    struct sockaddr_in addr = {0};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(UDP_PORT);
    addr.sin_addr.s_addr = INADDR_ANY;
    bind(sockfd, (struct sockaddr*)&addr, sizeof(addr));

    char reply[512];
    printf("UDP服务已启动，监听端口 %d\n", UDP_PORT);
    
    while (1) {
        char buf[256];
        struct sockaddr_in client_addr;
        socklen_t len = sizeof(client_addr);
        recvfrom(sockfd, buf, sizeof(buf), 0, (struct sockaddr*)&client_addr, &len);

        pthread_mutex_lock(&mem_mutex);
        build_json_response(reply, sizeof(reply), mr);
        pthread_mutex_unlock(&mem_mutex);

        sendto(sockfd, reply, strlen(reply), 0, (struct sockaddr*)&client_addr, len);
        
        char ip_str[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, ip_str, sizeof(ip_str));
        printf("向 %s:%d 发送MR信息\n", ip_str, ntohs(client_addr.sin_port));
    }
    
    close(sockfd);
    return NULL;
}

void handle_cm_event(struct rdma_cm_id *id) {
    struct rdma_conn_param conn_param = {0};
    conn_param.responder_resources = 1;
    conn_param.initiator_depth = 1;
    
    if (rdma_accept(id, &conn_param)) {
        fprintf(stderr, "Failed to accept RDMA connection\n");
        return;
    }

    char ip_str[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &((struct sockaddr_in*)&id->route.addr.dst_addr)->sin_addr, ip_str, sizeof(ip_str));
    printf("RDMA客户端已连接: %s\n", ip_str);
}

int main() {
    memset(shared_mem, 0, SHARED_MEM_SIZE);
    memset(last_mem, 0, SHARED_MEM_SIZE);
    
    struct ibv_device **dev_list = ibv_get_device_list(NULL);
    if (!dev_list || !dev_list[0]) {
        fprintf(stderr, "没有可用的RDMA设备\n");
        return -1;
    }

    ctx = ibv_open_device(dev_list[0]);
    pd = ibv_alloc_pd(ctx);
    mr = ibv_reg_mr(pd, shared_mem, SHARED_MEM_SIZE,
                   IBV_ACCESS_LOCAL_WRITE | 
                   IBV_ACCESS_REMOTE_READ | 
                   IBV_ACCESS_REMOTE_WRITE);
    
    printf("共享内存已注册: addr=%p, rkey=%u\n", mr->addr, mr->rkey);

    pthread_t udp_thread;
    pthread_create(&udp_thread, NULL, start_udp_server, NULL);

    struct rdma_event_channel *cm_channel = rdma_create_event_channel();
    struct rdma_cm_id *listener_id;
    rdma_create_id(cm_channel, &listener_id, NULL, RDMA_PS_TCP);
    
    struct sockaddr_in addr = {0};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(RDMA_PORT);
    addr.sin_addr.s_addr = INADDR_ANY;
    
    if (rdma_bind_addr(listener_id, (struct sockaddr*)&addr)) {
        perror("rdma_bind_addr");
        return -1;
    }
    
    rdma_listen(listener_id, 1);
    printf("RDMA服务已启动，监听端口 %d\n", RDMA_PORT);

    struct timespec sleep_time = {0, 100000000}; // 100ms
    while (1) {
        struct rdma_cm_event *event;
        if (!rdma_get_cm_event(cm_channel, &event)) {
            if (event->event == RDMA_CM_EVENT_CONNECT_REQUEST) {
                handle_cm_event(event->id);
            }
            rdma_ack_cm_event(event);
        }
        
        check_mem_changes();
        nanosleep(&sleep_time, NULL);
    }

    ibv_free_device_list(dev_list);
    return 0;
}
