#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <rdma/rdma_cma.h>
#include <rdma/rdma_verbs.h>

#define RDMA_PORT 4791
#define BUFFER_SIZE 1024

char shared_buffer[BUFFER_SIZE];
struct ibv_mr *mr;
struct ibv_pd *pd;

void handle_connection(struct rdma_cm_id *id) {
    printf("📞 处理新的RDMA连接请求\n");
    
    // 创建保护域
    pd = ibv_alloc_pd(id->verbs);
    if (!pd) {
        fprintf(stderr, "❌ 创建PD失败\n");
        return;
    }
    printf("✅ 保护域创建成功\n");
    
    // 注册内存区域
    mr = ibv_reg_mr(pd, shared_buffer, BUFFER_SIZE,
                   IBV_ACCESS_LOCAL_WRITE | 
                   IBV_ACCESS_REMOTE_READ | 
                   IBV_ACCESS_REMOTE_WRITE);
    if (!mr) {
        fprintf(stderr, "❌ 内存注册失败\n");
        return;
    }
    printf("✅ 内存区域注册成功: addr=%p, rkey=%u\n", mr->addr, mr->rkey);
    
    // 创建完成队列
    struct ibv_cq *cq = ibv_create_cq(id->verbs, 16, NULL, NULL, 0);
    if (!cq) {
        fprintf(stderr, "❌ 创建CQ失败\n");
        return;
    }
    printf("✅ 完成队列创建成功\n");
    
    // 创建QP
    struct ibv_qp_init_attr qp_attr = {0};
    qp_attr.send_cq = cq;
    qp_attr.recv_cq = cq;
    qp_attr.qp_type = IBV_QPT_RC;
    qp_attr.cap.max_send_wr = 16;
    qp_attr.cap.max_recv_wr = 16;
    qp_attr.cap.max_send_sge = 1;
    qp_attr.cap.max_recv_sge = 1;
    
    if (rdma_create_qp(id, pd, &qp_attr)) {
        fprintf(stderr, "❌ 创建QP失败\n");
        return;
    }
    printf("✅ QP创建成功: qp_num=%u\n", id->qp->qp_num);
    
    // 接受连接
    struct rdma_conn_param conn_param = {0};
    conn_param.responder_resources = 1;
    conn_param.initiator_depth = 1;
    
    if (rdma_accept(id, &conn_param)) {
        fprintf(stderr, "❌ 接受连接失败\n");
        return;
    }
    
    char ip_str[INET_ADDRSTRLEN];
    struct sockaddr_in *sin = (struct sockaddr_in*)rdma_get_peer_addr(id);
    inet_ntop(AF_INET, &sin->sin_addr, ip_str, sizeof(ip_str));
    printf("🎉 RDMA连接已建立: %s\n", ip_str);
}

int main() {
    printf("🚀 启动简化RDMA服务器\n");
    
    // 初始化共享缓冲区
    memset(shared_buffer, 0, BUFFER_SIZE);
    strcpy(shared_buffer, "RDMA_SERVER_INITIAL_DATA");
    printf("📝 初始化共享缓冲区: %s\n", shared_buffer);
    
    // 创建事件通道
    struct rdma_event_channel *cm_channel = rdma_create_event_channel();
    if (!cm_channel) {
        perror("❌ rdma_create_event_channel");
        return -1;
    }
    printf("✅ RDMA事件通道创建成功\n");
    
    // 创建CM ID
    struct rdma_cm_id *listener_id;
    if (rdma_create_id(cm_channel, &listener_id, NULL, RDMA_PS_TCP)) {
        perror("❌ rdma_create_id");
        return -1;
    }
    printf("✅ RDMA CM ID创建成功\n");
    
    // 绑定地址
    struct sockaddr_in addr = {0};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(RDMA_PORT);
    addr.sin_addr.s_addr = INADDR_ANY;
    
    printf("🔗 绑定地址 0.0.0.0:%d\n", RDMA_PORT);
    if (rdma_bind_addr(listener_id, (struct sockaddr*)&addr)) {
        perror("❌ rdma_bind_addr");
        return -1;
    }
    printf("✅ 地址绑定成功\n");
    
    // 开始监听
    if (rdma_listen(listener_id, 10)) {
        perror("❌ rdma_listen");
        return -1;
    }
    printf("🎧 RDMA服务器监听端口 %d\n", RDMA_PORT);
    printf("💡 请用 'sudo lsof -i:%d' 检查端口监听状态\n", RDMA_PORT);
    
    // 事件循环
    printf("⏳ 等待RDMA连接...\n");
    while (1) {
        struct rdma_cm_event *event;
        if (rdma_get_cm_event(cm_channel, &event) == 0) {
            printf("📨 收到RDMA事件: %s\n", rdma_event_str(event->event));
            
            switch (event->event) {
                case RDMA_CM_EVENT_CONNECT_REQUEST:
                    printf("🔔 新的连接请求\n");
                    handle_connection(event->id);
                    break;
                    
                case RDMA_CM_EVENT_ESTABLISHED:
                    printf("🎉 连接已建立\n");
                    break;
                    
                case RDMA_CM_EVENT_DISCONNECTED:
                    printf("📴 连接已断开\n");
                    break;
                    
                default:
                    printf("❓ 未处理的事件: %d\n", event->event);
                    break;
            }
            
            rdma_ack_cm_event(event);
        } else {
            if (errno != EAGAIN) {
                perror("rdma_get_cm_event");
                break;
            }
        }
        
        // 检查共享缓冲区变化
        static char last_buffer[BUFFER_SIZE] = {0};
        if (memcmp(shared_buffer, last_buffer, BUFFER_SIZE) != 0) {
            printf("🔄 检测到内存变化: %s\n", shared_buffer);
            memcpy(last_buffer, shared_buffer, BUFFER_SIZE);
        }
        
        usleep(100000); // 100ms
    }
    
    return 0;
}
