#!/usr/bin/env python3
"""
内存访问测试 - 验证是否能真正访问远程内存
"""

import socket
import json
import time
import struct

def get_server_mr_info(server_ip):
    """获取服务器MR信息"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.sendto(b"GET_MR", (server_ip, 8888))
    response = sock.recv(1024).decode('utf-8')
    sock.close()
    
    mr_info = json.loads(response)
    return {
        'addr': int(mr_info["mr_addr"], 16),
        'rkey': mr_info["rkey"],
        'size': mr_info["size"]
    }

def test_memory_access_via_proc(mr_info):
    """尝试通过/proc/mem直接访问内存（需要root权限）"""
    print("🔍 测试直接内存访问...")
    
    try:
        # 这种方法需要root权限，通常不可行
        with open('/proc/self/mem', 'r+b') as mem:
            mem.seek(mr_info['addr'])
            data = mem.read(64)
            print(f"   读取到数据: {data}")
            return True
    except Exception as e:
        print(f"   直接内存访问失败: {e}")
        return False

def test_memory_monitoring(server_ip):
    """测试服务器内存监控功能"""
    print("🔍 测试服务器内存监控...")
    
    # 发送一个特殊的测试模式请求
    test_patterns = [
        b"MEMORY_TEST_PATTERN_1",
        b"MEMORY_TEST_PATTERN_2", 
        b"READ_REQ_0000016",  # 服务器代码中的特殊模式
    ]
    
    for i, pattern in enumerate(test_patterns):
        print(f"   测试模式 {i+1}: {pattern}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.sendto(pattern, (server_ip, 8888))
            
            # 等待一下让服务器处理
            time.sleep(0.5)
            
            # 获取响应
            sock.settimeout(2)
            try:
                response = sock.recv(1024).decode('utf-8')
                print(f"   服务器响应: {response}")
            except socket.timeout:
                print("   无响应（可能是单向通信）")
            
            sock.close()
            
        except Exception as e:
            print(f"   模式 {i+1} 测试失败: {e}")

def test_rdma_simulation(server_ip, mr_info):
    """模拟RDMA操作并验证效果"""
    print("🔍 模拟RDMA操作测试...")
    
    # 模拟RDMA WRITE - 直接向服务器内存地址发送数据
    test_data = b"SIMULATED_RDMA_WRITE_DATA"
    
    print(f"   模拟写入数据: {test_data}")
    print(f"   目标地址: {hex(mr_info['addr'])}")
    print(f"   RKEY: {mr_info['rkey']}")
    
    # 发送模拟的RDMA写入请求
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    # 构造一个包含地址信息的请求
    write_request = {
        "operation": "RDMA_WRITE_SIM",
        "target_addr": hex(mr_info['addr']),
        "rkey": mr_info['rkey'],
        "data": test_data.decode('utf-8', errors='ignore'),
        "length": len(test_data)
    }
    
    request_json = json.dumps(write_request)
    sock.sendto(request_json.encode(), (server_ip, 8888))
    
    try:
        sock.settimeout(3)
        response = sock.recv(1024).decode('utf-8')
        print(f"   写入响应: {response}")
    except socket.timeout:
        print("   写入请求已发送（无响应）")
    
    sock.close()
    
    # 等待一下
    time.sleep(1)
    
    # 模拟RDMA READ - 请求读取内存内容
    print("   模拟读取操作...")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    read_request = {
        "operation": "RDMA_READ_SIM",
        "target_addr": hex(mr_info['addr']),
        "rkey": mr_info['rkey'],
        "length": len(test_data)
    }
    
    request_json = json.dumps(read_request)
    sock.sendto(request_json.encode(), (server_ip, 8888))
    
    try:
        sock.settimeout(3)
        response = sock.recv(1024).decode('utf-8')
        print(f"   读取响应: {response}")
        
        # 尝试解析响应
        try:
            read_data = json.loads(response)
            if 'data' in read_data:
                print(f"   读取到的数据: {read_data['data']}")
                return test_data.decode('utf-8', errors='ignore') in str(read_data['data'])
        except:
            pass
            
    except socket.timeout:
        print("   读取请求超时")
    
    sock.close()
    return False

def main():
    print("🚀 内存访问验证测试")
    print("=" * 50)
    
    server_ip = "***********"
    
    try:
        # 1. 获取服务器MR信息
        print("1️⃣ 获取服务器MR信息...")
        mr_info = get_server_mr_info(server_ip)
        print(f"   MR地址: {hex(mr_info['addr'])}")
        print(f"   RKEY: {mr_info['rkey']}")
        print(f"   大小: {mr_info['size']}")
        print()
        
        # 2. 测试内存监控
        print("2️⃣ 测试服务器内存监控...")
        test_memory_monitoring(server_ip)
        print()
        
        # 3. 模拟RDMA操作
        print("3️⃣ 模拟RDMA操作...")
        rdma_success = test_rdma_simulation(server_ip, mr_info)
        print()
        
        # 4. 尝试直接内存访问（通常会失败）
        print("4️⃣ 尝试直接内存访问...")
        direct_success = test_memory_access_via_proc(mr_info)
        print()
        
        print("=" * 50)
        print("📋 测试结果总结:")
        print(f"   服务器MR信息获取: ✅")
        print(f"   内存监控测试: ✅")
        print(f"   RDMA模拟操作: {'✅' if rdma_success else '❌'}")
        print(f"   直接内存访问: {'✅' if direct_success else '❌'}")
        
        print("\n🔍 结论:")
        if rdma_success:
            print("   ✅ 能够通过模拟方式验证内存访问")
        else:
            print("   ❌ 无法验证真正的内存访问")
            print("   💡 建议: 需要真正的RDMA连接才能进行远程内存操作")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
